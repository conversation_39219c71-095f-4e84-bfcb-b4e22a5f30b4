kind: pipeline
type: docker
name: deploy-on-main

trigger:
  branch:
    - main
  event:
    - push
      

steps:
  - name: build-and-run
    image: docker:27-cli
    volumes:
      - name: app
        path: /srv/app/volumes
    environment:
      SOME_ENV: SomeEnv
      SOME_SECRET:
        from_secret: SOME_SECRET
    commands:
      - apk add --no-cache rsync
      - rsync -a --delete --exclude '.git' /drone/src/ /srv/app/
      - docker compose --project-directory /srv/app/ up -d --build --pull always

volumes:
  - name: app
    host:
      path: /home/<USER>/docker/appname
