using Serilog;
using TestCiCdGithubActions.WebApi.Middleware;

namespace TestCiCdGithubActions.WebApi.Extensions;

/// <summary>
/// Расширения для настройки конвейера обработки HTTP-запросов в WebApi.
/// Предоставляет централизованную точку для настройки всех компонентов WebApi.
/// </summary>
public static class WebApiWebApplicationExtensions
{
    /// <summary>
    /// Настраивает конвейер обработки HTTP-запросов для WebApi.
    /// </summary>
    public static WebApplication UseWebApi(this WebApplication app)
    {
        app.UseLogging();
        
        app.MapControllers();

        return app;
    }

    /// <summary>
    /// Добавляет логирование в конвейер обработки запросов.
    /// </summary>
    private static WebApplication UseLogging(this WebApplication app)
    {
        app.UseMiddleware<EnricherMiddleware>();
        app.UseSerilogRequestLogging();

        return app;
    }
}