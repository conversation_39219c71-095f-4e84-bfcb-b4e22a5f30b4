using Serilog;

namespace TestCiCdGithubActions.WebApi.Extensions;

/// <summary>
/// Расширения для регистрации сервисов уровня WebApi в контейнере зависимостей.
/// Предоставляет централизованную точку для настройки всех компонентов WebApi.
/// </summary>
public static class WebApiServiceCollectionExtensions
{
    /// <summary>
    /// Регистрирует сервисы WebApi в контейнере зависимостей.
    /// </summary>
    public static IServiceCollection AddWebApi(
        this IServiceCollection services,
        IConfiguration configuration,
        IHostBuilder host)
    {
        services.AddControllers();
        
        AddLogging(services, configuration, host);

        return services;
    }

    /// <summary>
    /// Регистрирует логирование в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddLogging(
        this IServiceCollection services,
        IConfiguration configuration,
        IHostBuilder host)
    {
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Enrich.FromLogContext()
            .CreateLogger();

        host.UseSerilog();

        return services;
    }
}