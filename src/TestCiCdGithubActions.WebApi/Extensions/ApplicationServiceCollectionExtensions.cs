namespace TestCiCdGithubActions.WebApi.Extensions;

/// <summary>
/// Расширения для регистрации сервисов уровня Application в контейнере зависимостей.
/// Предоставляет централизованную точку для настройки всех компонентов Application.
/// </summary>
public static class ApplicationServiceCollectionExtensions
{
    /// <summary>
    /// Регистрирует сервисы Application в контейнере зависимостей.
    /// </summary>
    public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }
}