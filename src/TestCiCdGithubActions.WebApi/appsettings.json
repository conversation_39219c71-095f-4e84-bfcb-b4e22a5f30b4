{"AllowedHosts": "*", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.Hosting": "Information"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithProcessId"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {}}], "Properties": {"Application": "TestCiCdGithubActions"}}}