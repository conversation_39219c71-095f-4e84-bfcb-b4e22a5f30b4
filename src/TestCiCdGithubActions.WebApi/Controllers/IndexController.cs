using Microsoft.AspNetCore.Mvc;

namespace TestCiCdGithubActions.WebApi.Controllers;

[ApiController]
[Route("/")]
public class IndexController : Controller
{
    [HttpGet]
    public string IndexGet(IConfiguration configuration)
    {
        var path = Path.Combine(AppContext.BaseDirectory, "./volumes");
        
        Directory.CreateDirectory(path);
        
        var filePath = Path.Combine(path, "test.txt");
        
        System.IO.File.WriteAllText(filePath, "Hello World!");
        
        return $"Service is up and running! Secret: {configuration["SOME_SECRET"]} Env: {configuration["SOME_ENV"]}";
    }
}
