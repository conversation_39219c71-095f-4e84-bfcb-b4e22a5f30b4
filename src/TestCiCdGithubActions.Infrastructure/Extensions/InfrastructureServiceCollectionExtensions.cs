using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace TestCiCdGithubActions.Infrastructure.Extensions;

/// <summary>
/// Методы расширения для регистрации сервисов уровня инфраструктуры в контейнере зависимостей.
/// Предоставляет централизованную точку для настройки всех компонентов инфраструктуры.
/// </summary>
public static class InfrastructureServiceCollectionExtensions
{
    /// <summary>
    /// Регистрирует сервисы инфраструктуры в контейнере зависимостей.
    /// </summary>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }
}